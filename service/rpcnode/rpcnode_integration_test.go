package rpcnode

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"net/http/httputil"
	"os"
	"sentioxyz/sentio/service/rpcnode/repository"
	"strings"
	"testing"
	"time"

	redisrate "github.com/go-redis/redis_rate/v10"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/datatypes"

	"sentioxyz/sentio/chain/evm/chaininfo"
	"sentioxyz/sentio/service/rpcnode/model"
)

// MockUpstreamServer represents a mock upstream RPC server
type MockUpstreamServer struct {
	*httptest.Server
	ReceivedRequests []MockRequest
}

type MockRequest struct {
	Method      string
	URL         string
	Headers     http.Header
	Body        string
	QueryParams map[string][]string
}

// NewMockUpstreamServer creates a new mock upstream server
func NewMockUpstreamServer(responseBody string, statusCode int) *MockUpstreamServer {
	mock := &MockUpstreamServer{
		ReceivedRequests: make([]MockRequest, 0),
	}

	mock.Server = httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Record the request
		body, _ := io.ReadAll(r.Body)
		r.Body.Close()

		mock.ReceivedRequests = append(mock.ReceivedRequests, MockRequest{
			Method:      r.Method,
			URL:         r.URL.String(),
			Headers:     r.Header.Clone(),
			Body:        string(body),
			QueryParams: r.URL.Query(),
		})

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(statusCode)
		w.Write([]byte(responseBody))
	}))

	return mock
}

// GetLastRequest returns the last received request
func (m *MockUpstreamServer) GetLastRequest() *MockRequest {
	if len(m.ReceivedRequests) == 0 {
		return nil
	}
	return &m.ReceivedRequests[len(m.ReceivedRequests)-1]
}

// Reset clears all recorded requests
func (m *MockUpstreamServer) Reset() {
	m.ReceivedRequests = make([]MockRequest, 0)
}

func NewTestService(settings model.RPCSettings) *Service {
	return &Service{
		settings: settings,
		proxies:  make(map[string]*httputil.ReverseProxy),
		repo:     newMockRepository(),
		limiter:  newMockLimiter(),
	}
}

func newMockLimiter() *redisrate.Limiter {
	return nil
}

// newMockRepository creates a mock repository for testing
func newMockRepository() *repository.RPCNodeRepository {
	// Mock implementation of RPCNodeRepository for testing
	return &repository.RPCNodeRepository{}
}

// RPCNodeIntegrationTestSuite is the test suite for RPC Node integration tests
type RPCNodeIntegrationTestSuite struct {
	suite.Suite
	service           *Service
	mockServer        *MockUpstreamServer
	defaultServer     *MockUpstreamServer
	originalChainInfo map[string]*chaininfo.EthChainInfo
}

// SetupSuite runs once before all tests in the suite
func (suite *RPCNodeIntegrationTestSuite) SetupSuite() {
	// Store original chaininfo to restore later
	suite.originalChainInfo = chaininfo.SlugToInfo

	// Setup default mock server
	suite.defaultServer = NewMockUpstreamServer(`{"result":"default_response"}`, 200)

	// Setup main mock server
	suite.mockServer = NewMockUpstreamServer(`{"result":"test_response"}`, 200)
}

// TearDownSuite runs once after all tests in the suite
func (suite *RPCNodeIntegrationTestSuite) TearDownSuite() {
	// Restore original chaininfo
	chaininfo.SlugToInfo = suite.originalChainInfo

	// Close mock servers
	if suite.defaultServer != nil {
		suite.defaultServer.Close()
	}
	if suite.mockServer != nil {
		suite.mockServer.Close()
	}
}

// SetupTest runs before each test
func (suite *RPCNodeIntegrationTestSuite) SetupTest() {
	// Reset mock servers
	suite.mockServer.Reset()
	suite.defaultServer.Reset()

	// Create service with server default.yaml
	f, err := os.ReadFile("server/default.yaml")
	if err != nil {
		suite.T().Fatalf("Failed to read default.yaml: %v", err)
	}
	settings, err := model.LoadSettings(f)
	suite.service = NewTestService(settings)
}

// Helper method to create a request context
func (suite *RPCNodeIntegrationTestSuite) createRequestContext(req *http.Request) *http.Request {
	rCtx := &requestContext{
		start:        time.Now(),
		method:       req.Method,
		originUrl:    req.URL.String(),
		requestBody:  &bytes.Buffer{},
		responseBody: &bytes.Buffer{},
	}
	ctx := context.WithValue(req.Context(), "rCtx", rCtx)
	return req.WithContext(ctx)
}

// Helper method to execute a proxy request
func (suite *RPCNodeIntegrationTestSuite) executeProxyRequest(node *model.RPCNode, slug string, requestBody string) (*httptest.ResponseRecorder, error) {
	req := httptest.NewRequest("POST", fmt.Sprintf("/test-code/%s", slug), strings.NewReader(requestBody))
	req.Header.Set("Content-Type", "application/json")
	req = suite.createRequestContext(req)

	network, rpcEndpoint, proxy, err := suite.service.GetRPCProxy(node, slug)
	if err != nil {
		return nil, err
	}

	// Set context values
	rCtx := req.Context().Value("rCtx").(*requestContext)
	rCtx.node = node
	rCtx.endpoint = rpcEndpoint
	rCtx.network = network

	recorder := httptest.NewRecorder()
	proxy.ServeHTTP(recorder, req)

	return recorder, nil
}

// TestBasicRequest tests basic HTTP request proxying functionality
func (suite *RPCNodeIntegrationTestSuite) TestBasicRequest() {
	// Create test node
	node := &model.RPCNode{
		ID:       "test-node",
		Networks: datatypes.JSONSlice[string]{"1"},
		Enabled:  true,
		Code:     "test-code",
	}

	// Create test request
	requestBody := `{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}`
	req := httptest.NewRequest("POST", "/test-code/ethereum", strings.NewReader(requestBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Custom-Header", "test-value")
	req = suite.createRequestContext(req)

	// Get proxy and setup context
	network, rpcEndpoint, proxy, err := suite.service.GetRPCProxy(node, "ethereum")
	suite.Require().NoError(err)
	suite.Require().NotNil(proxy)

	// Set context values
	rCtx := req.Context().Value("rCtx").(*requestContext)
	rCtx.node = node
	rCtx.endpoint = rpcEndpoint
	rCtx.network = network

	// Execute proxy request
	recorder := httptest.NewRecorder()
	proxy.ServeHTTP(recorder, req)

	// Verify response
	suite.Equal(200, recorder.Code)
	suite.Contains(recorder.Body.String(), "result")
	suite.Equal("application/json", recorder.Header().Get("Content-Type"))

	// Verify upstream received the request
	suite.Require().Len(suite.mockServer.ReceivedRequests, 1)
	upstreamReq := suite.mockServer.GetLastRequest()
	suite.Equal("POST", upstreamReq.Method)
	suite.Equal(requestBody, upstreamReq.Body)
	suite.Equal("test-value", upstreamReq.Headers.Get("X-Custom-Header"))
	suite.Equal("application/json", upstreamReq.Headers.Get("Content-Type"))
}

// TestQueryParameters tests GET requests with query parameter handling
func (suite *RPCNodeIntegrationTestSuite) TestQueryParameters() {
	node := &model.RPCNode{
		ID:       "test-node",
		Networks: datatypes.JSONSlice[string]{"*"},
		Enabled:  true,
	}

	// Create request with query parameters
	req := httptest.NewRequest("GET", "/test-code/polygon?param1=value1&param2=value2", nil)
	req = suite.createRequestContext(req)

	// Get proxy and setup context
	network, rpcEndpoint, proxy, err := suite.service.GetRPCProxy(node, "polygon")
	suite.Require().NoError(err)

	// Set context values
	rCtx := req.Context().Value("rCtx").(*requestContext)
	rCtx.node = node
	rCtx.endpoint = rpcEndpoint
	rCtx.network = network

	// Execute proxy request
	recorder := httptest.NewRecorder()
	proxy.ServeHTTP(recorder, req)

	// Verify response
	suite.Equal(200, recorder.Code)

	// Verify upstream received query parameters
	suite.Require().GreaterOrEqual(len(suite.defaultServer.ReceivedRequests), 1)
	upstreamReq := suite.defaultServer.GetLastRequest()
	suite.Equal("GET", upstreamReq.Method)
	suite.Equal([]string{"value1"}, upstreamReq.QueryParams["param1"])
	suite.Equal([]string{"value2"}, upstreamReq.QueryParams["param2"])
}

// TestErrorHandling tests error response proxying
func (suite *RPCNodeIntegrationTestSuite) TestErrorHandling() {
	// Setup error server
	errorServer := NewMockUpstreamServer(`{"error":"internal server error"}`, 500)
	defer errorServer.Close()

	// Create service with error server as default upstream
	settings := model.RPCSettings{
		DefaultUpstream: errorServer.URL,
	}
	service := NewTestService(settings)

	node := &model.RPCNode{
		ID:       "test-node",
		Networks: datatypes.JSONSlice[string]{"1"},
		Enabled:  true,
	}

	// Create test request
	req := httptest.NewRequest("POST", "/test-code/mainnet", strings.NewReader(`{"test":"data"}`))
	req.Header.Set("Content-Type", "application/json")

	// Add request context
	rCtx := &requestContext{
		start:        time.Now(),
		method:       "POST",
		originUrl:    req.URL.String(),
		requestBody:  &bytes.Buffer{},
		responseBody: &bytes.Buffer{},
	}
	ctx := context.WithValue(req.Context(), "rCtx", rCtx)
	req = req.WithContext(ctx)

	// Get proxy and setup context
	network, rpcEndpoint, proxy, err := service.GetRPCProxy(node, "mainnet")
	suite.Require().NoError(err)

	rCtx.node = node
	rCtx.endpoint = rpcEndpoint
	rCtx.network = network

	// Execute proxy request
	recorder := httptest.NewRecorder()
	proxy.ServeHTTP(recorder, req)

	// Verify error response is proxied correctly
	suite.Equal(500, recorder.Code)
	suite.Equal(`{"error":"internal server error"}`, recorder.Body.String())

	// Verify upstream received the request
	suite.Require().Len(errorServer.ReceivedRequests, 1)
	upstreamReq := errorServer.GetLastRequest()
	suite.Equal("POST", upstreamReq.Method)
	suite.Equal(`{"test":"data"}`, upstreamReq.Body)
}

func TestService_ProxyIntegration_MultipleUpstreams(t *testing.T) {
	// Setup multiple mock upstream servers
	ethServer := NewMockUpstreamServer(`{"result":"eth_response"}`, 200)
	defer ethServer.Close()

	polygonServer := NewMockUpstreamServer(`{"result":"polygon_response"}`, 200)
	defer polygonServer.Close()

	// Setup service with multiple networks using different paths to ensure different proxies
	settings := model.RPCSettings{
		DefaultUpstream: ethServer.URL,
		Networks: []model.RPCNetwork{
			{
				ID:   "1",
				Name: "Ethereum",
				Endpoints: []model.RPCEndpoint{
					{
						Path:        "/mainnet",
						Name:        "Ethereum Mainnet",
						Upstream:    ethServer.URL + "/eth",
						RateLimitID: "default",
					},
				},
			},
			{
				ID:   "137",
				Name: "Polygon",
				Endpoints: []model.RPCEndpoint{
					{
						Path:        "/polygon",
						Name:        "Polygon Mainnet",
						Upstream:    polygonServer.URL + "/poly",
						RateLimitID: "default",
					},
				},
			},
		},
	}

	service := NewTestService(settings)

	// Setup chaininfo - use different slugs that match the endpoint paths
	chaininfo.SlugToInfo = map[string]*chaininfo.EthChainInfo{
		"mainnet": {
			ChainID: "1",
			Name:    "Ethereum",
			Slug:    "mainnet",
		},
		"polygon": {
			ChainID: "137",
			Name:    "Polygon",
			Slug:    "polygon",
		},
	}

	node := &model.RPCNode{
		ID:       "test-node",
		Networks: datatypes.JSONSlice[string]{"1", "137"},
		Enabled:  true,
	}

	// Test Ethereum request
	ethReq := httptest.NewRequest("POST", "/test-code/mainnet", strings.NewReader(`{"method":"eth_blockNumber"}`))
	ethRCtx := &requestContext{
		start:        time.Now(),
		method:       "POST",
		originUrl:    ethReq.URL.String(),
		requestBody:  &bytes.Buffer{},
		responseBody: &bytes.Buffer{},
	}
	ethCtx := context.WithValue(ethReq.Context(), "rCtx", ethRCtx)
	ethReq = ethReq.WithContext(ethCtx)

	ethNetwork, ethEndpoint, ethProxy, err := service.GetRPCProxy(node, "mainnet")
	require.NoError(t, err)
	ethRCtx.node = node
	ethRCtx.endpoint = ethEndpoint
	ethRCtx.network = ethNetwork

	ethRecorder := httptest.NewRecorder()
	ethProxy.ServeHTTP(ethRecorder, ethReq)

	// Test Polygon request
	polyReq := httptest.NewRequest("POST", "/test-code/polygon", strings.NewReader(`{"method":"eth_chainId"}`))
	polyRCtx := &requestContext{
		start:        time.Now(),
		method:       "POST",
		originUrl:    polyReq.URL.String(),
		requestBody:  &bytes.Buffer{},
		responseBody: &bytes.Buffer{},
	}
	polyCtx := context.WithValue(polyReq.Context(), "rCtx", polyRCtx)
	polyReq = polyReq.WithContext(polyCtx)

	polyNetwork, polyEndpoint, polyProxy, err := service.GetRPCProxy(node, "polygon")
	require.NoError(t, err)
	polyRCtx.node = node
	polyRCtx.endpoint = polyEndpoint
	polyRCtx.network = polyNetwork

	polyRecorder := httptest.NewRecorder()
	polyProxy.ServeHTTP(polyRecorder, polyReq)

	// Verify responses
	assert.Equal(t, 200, ethRecorder.Code)
	assert.Equal(t, 200, polyRecorder.Code)

	// Verify that requests were properly routed
	// Since both networks might use default upstream, let's just verify the requests were made
	totalRequests := len(ethServer.ReceivedRequests) + len(polygonServer.ReceivedRequests)
	assert.GreaterOrEqual(t, totalRequests, 2, "Should have received at least 2 total requests")

	// Verify request content
	allRequests := append(ethServer.ReceivedRequests, polygonServer.ReceivedRequests...)
	assert.Len(t, allRequests, 2, "Should have exactly 2 requests total")

	// Check that we have both types of requests
	var hasEthBlockNumber, hasEthChainId bool
	for _, req := range allRequests {
		if strings.Contains(req.Body, "eth_blockNumber") {
			hasEthBlockNumber = true
		}
		if strings.Contains(req.Body, "eth_chainId") {
			hasEthChainId = true
		}
	}
	assert.True(t, hasEthBlockNumber, "Should have received eth_blockNumber request")
	assert.True(t, hasEthChainId, "Should have received eth_chainId request")
}

func TestService_ProxyIntegration_RequestHeaders(t *testing.T) {
	// Setup mock upstream server
	mockServer := NewMockUpstreamServer(`{"result":"success"}`, 200)
	defer mockServer.Close()

	// Setup service
	settings := model.RPCSettings{
		DefaultUpstream: mockServer.URL,
	}
	service := NewTestService(settings)

	// Setup chaininfo
	chaininfo.SlugToInfo = map[string]*chaininfo.EthChainInfo{
		"mainnet": {
			ChainID: "1",
			Name:    "Ethereum",
			Slug:    "mainnet",
		},
	}

	node := &model.RPCNode{
		ID:       "test-node",
		Networks: datatypes.JSONSlice[string]{"*"},
		Enabled:  true,
	}

	// Create request with various headers
	req := httptest.NewRequest("POST", "/test-code/mainnet", strings.NewReader(`{"test":"data"}`))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer token123")
	req.Header.Set("X-API-Key", "api-key-456")
	req.Header.Set("User-Agent", "test-client/1.0")
	req.Host = "rpc.example.com"

	// Add request context
	rCtx := &requestContext{
		start:        time.Now(),
		method:       "POST",
		originUrl:    req.URL.String(),
		requestBody:  &bytes.Buffer{},
		responseBody: &bytes.Buffer{},
	}
	ctx := context.WithValue(req.Context(), "rCtx", rCtx)
	req = req.WithContext(ctx)

	// Get proxy and setup context
	network, rpcEndpoint, proxy, err := service.GetRPCProxy(node, "mainnet")
	require.NoError(t, err)

	rCtx.node = node
	rCtx.endpoint = rpcEndpoint
	rCtx.network = network

	// Execute proxy request
	recorder := httptest.NewRecorder()
	proxy.ServeHTTP(recorder, req)

	// Verify response
	assert.Equal(t, 200, recorder.Code)

	// Verify upstream received headers correctly
	require.Len(t, mockServer.ReceivedRequests, 1)
	upstreamReq := mockServer.GetLastRequest()

	// Check that important headers are forwarded
	assert.Equal(t, "application/json", upstreamReq.Headers.Get("Content-Type"))
	assert.Equal(t, "Bearer token123", upstreamReq.Headers.Get("Authorization"))
	assert.Equal(t, "api-key-456", upstreamReq.Headers.Get("X-API-Key"))
	assert.Equal(t, "test-client/1.0", upstreamReq.Headers.Get("User-Agent"))

	// Check that X-Forwarded-Host is set
	assert.Equal(t, "rpc.example.com", upstreamReq.Headers.Get("X-Forwarded-Host"))
}

func TestService_ProxyIntegration_ForkNode(t *testing.T) {
	// Setup mock fork server
	forkServer := NewMockUpstreamServer(`{"result":"fork_response"}`, 200)
	defer forkServer.Close()

	// Setup service
	settings := model.RPCSettings{
		DefaultUpstream: "https://default.example.com",
	}
	service := NewTestService(settings)

	// Create fork node
	node := &model.RPCNode{
		ID:          "fork-node",
		Network:     "1", // Using legacy Network field for fork nodes
		Networks:    datatypes.JSONSlice[string]{"1"},
		Enabled:     true,
		ForkID:      "test-fork-123",
		ForkNodeUrl: forkServer.URL + "/fork",
	}

	// Create test request
	req := httptest.NewRequest("POST", "/test-code/mainnet", strings.NewReader(`{"method":"eth_getBalance"}`))
	req.Header.Set("Content-Type", "application/json")

	// Add request context
	rCtx := &requestContext{
		start:        time.Now(),
		method:       "POST",
		originUrl:    req.URL.String(),
		requestBody:  &bytes.Buffer{},
		responseBody: &bytes.Buffer{},
	}
	ctx := context.WithValue(req.Context(), "rCtx", rCtx)
	req = req.WithContext(ctx)

	// Get fork proxy
	network, rpcEndpoint, proxy, err := service.getForkProxy(node)
	require.NoError(t, err)
	require.NotNil(t, proxy)

	rCtx.node = node
	rCtx.endpoint = rpcEndpoint
	rCtx.network = network

	// Execute proxy request
	recorder := httptest.NewRecorder()
	proxy.ServeHTTP(recorder, req)

	// Verify response
	assert.Equal(t, 200, recorder.Code)
	assert.Equal(t, `{"result":"fork_response"}`, recorder.Body.String())

	// Verify fork server received the request
	require.Len(t, forkServer.ReceivedRequests, 1)
	upstreamReq := forkServer.GetLastRequest()
	assert.Equal(t, "POST", upstreamReq.Method)
	assert.Equal(t, `{"method":"eth_getBalance"}`, upstreamReq.Body)
	assert.Contains(t, upstreamReq.URL, "/fork")
}

func TestService_ProxyIntegration_LargeRequestBody(t *testing.T) {
	// Setup mock upstream server
	mockServer := NewMockUpstreamServer(`{"result":"large_response"}`, 200)
	defer mockServer.Close()

	// Setup service
	settings := model.RPCSettings{
		DefaultUpstream: mockServer.URL,
	}
	service := NewTestService(settings)

	// Setup chaininfo
	chaininfo.SlugToInfo = map[string]*chaininfo.EthChainInfo{
		"mainnet": {
			ChainID: "1",
			Name:    "Ethereum",
			Slug:    "mainnet",
		},
	}

	node := &model.RPCNode{
		ID:       "test-node",
		Networks: datatypes.JSONSlice[string]{"1"},
		Enabled:  true,
	}

	// Create large request body (simulate batch RPC request)
	largeRequest := map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  "eth_getLogs",
		"params": []interface{}{
			map[string]interface{}{
				"fromBlock": "0x0",
				"toBlock":   "latest",
				"topics":    make([]string, 1000), // Large array
			},
		},
		"id": 1,
	}

	requestBody, err := json.Marshal(largeRequest)
	require.NoError(t, err)

	req := httptest.NewRequest("POST", "/test-code/mainnet", bytes.NewReader(requestBody))
	req.Header.Set("Content-Type", "application/json")

	// Add request context
	rCtx := &requestContext{
		start:        time.Now(),
		method:       "POST",
		originUrl:    req.URL.String(),
		requestBody:  &bytes.Buffer{},
		responseBody: &bytes.Buffer{},
	}
	ctx := context.WithValue(req.Context(), "rCtx", rCtx)
	req = req.WithContext(ctx)

	// Get proxy and setup context
	network, rpcEndpoint, proxy, err := service.GetRPCProxy(node, "mainnet")
	require.NoError(t, err)

	rCtx.node = node
	rCtx.endpoint = rpcEndpoint
	rCtx.network = network

	// Execute proxy request
	recorder := httptest.NewRecorder()
	proxy.ServeHTTP(recorder, req)

	// Verify response
	assert.Equal(t, 200, recorder.Code)
	assert.Equal(t, `{"result":"large_response"}`, recorder.Body.String())

	// Verify upstream received the large request correctly
	require.Len(t, mockServer.ReceivedRequests, 1)
	upstreamReq := mockServer.GetLastRequest()
	assert.Equal(t, "POST", upstreamReq.Method)

	// Parse and verify the request body structure
	var receivedRequest map[string]interface{}
	err = json.Unmarshal([]byte(upstreamReq.Body), &receivedRequest)
	require.NoError(t, err)
	assert.Equal(t, "eth_getLogs", receivedRequest["method"])
	assert.Equal(t, "2.0", receivedRequest["jsonrpc"])
}

func TestService_ProxyIntegration_NetworkConfiguration(t *testing.T) {
	// Setup mock upstream server
	mockServer := NewMockUpstreamServer(`{"result":"network_test"}`, 200)
	defer mockServer.Close()

	// Setup service with specific network configuration
	settings := model.RPCSettings{
		DefaultUpstream: mockServer.URL,
		Networks: []model.RPCNetwork{
			{
				ID:   "1",
				Name: "Ethereum",
				Endpoints: []model.RPCEndpoint{
					{
						Path:        "/mainnet",
						Name:        "Ethereum Mainnet",
						Upstream:    mockServer.URL + "/eth",
						RateLimitID: "default",
					},
				},
			},
		},
	}

	service := NewTestService(settings)

	node := &model.RPCNode{
		ID:       "test-node",
		Networks: datatypes.JSONSlice[string]{"1"},
		Enabled:  true,
	}

	// Create request for the configured endpoint path
	req := httptest.NewRequest("POST", "/test-code/mainnet", strings.NewReader(`{"test":"network"}`))
	req.Header.Set("Content-Type", "application/json")

	// Add request context
	rCtx := &requestContext{
		start:        time.Now(),
		method:       "POST",
		originUrl:    req.URL.String(),
		requestBody:  &bytes.Buffer{},
		responseBody: &bytes.Buffer{},
	}
	ctx := context.WithValue(req.Context(), "rCtx", rCtx)
	req = req.WithContext(ctx)

	// Get proxy for mainnet (should match the configured endpoint)
	network, rpcEndpoint, proxy, err := service.GetRPCProxy(node, "mainnet")
	require.NoError(t, err)

	// Verify network configuration
	assert.Equal(t, "1", network.ID)
	assert.Equal(t, "Ethereum", network.Name)
	assert.Equal(t, "/mainnet", rpcEndpoint.Path)
	assert.Equal(t, "Ethereum Mainnet", rpcEndpoint.Name)

	rCtx.node = node
	rCtx.endpoint = rpcEndpoint
	rCtx.network = network

	// Execute proxy request
	recorder := httptest.NewRecorder()
	proxy.ServeHTTP(recorder, req)

	// Verify response
	assert.Equal(t, 200, recorder.Code)
	assert.Equal(t, `{"result":"network_test"}`, recorder.Body.String())

	// Verify upstream received request
	require.Len(t, mockServer.ReceivedRequests, 1)
	upstreamReq := mockServer.GetLastRequest()
	assert.Equal(t, "POST", upstreamReq.Method)
	assert.Equal(t, `{"test":"network"}`, upstreamReq.Body)
}

func TestService_ProxyIntegration_ResponseHeaders(t *testing.T) {
	// Setup mock upstream server that returns custom headers
	mockServer := NewMockUpstreamServer(`{"result":"header_test"}`, 200)
	defer mockServer.Close()

	// Modify the mock server to add custom response headers
	mockServer.Server.Config.Handler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Record the request
		body, _ := io.ReadAll(r.Body)
		r.Body.Close()

		mockServer.ReceivedRequests = append(mockServer.ReceivedRequests, MockRequest{
			Method:      r.Method,
			URL:         r.URL.String(),
			Headers:     r.Header.Clone(),
			Body:        string(body),
			QueryParams: r.URL.Query(),
		})

		// Set custom response headers
		w.Header().Set("Content-Type", "application/json")
		w.Header().Set("X-Custom-Response", "custom-value")
		w.Header().Set("Cache-Control", "no-cache")
		w.Header().Set("X-Rate-Limit-Remaining", "100")
		w.WriteHeader(200)
		w.Write([]byte(`{"result":"header_test"}`))
	})

	// Setup service
	settings := model.RPCSettings{
		DefaultUpstream: mockServer.URL,
	}
	service := NewTestService(settings)

	// Setup chaininfo
	chaininfo.SlugToInfo = map[string]*chaininfo.EthChainInfo{
		"mainnet": {
			ChainID: "1",
			Name:    "Ethereum",
			Slug:    "mainnet",
		},
	}

	node := &model.RPCNode{
		ID:       "test-node",
		Networks: datatypes.JSONSlice[string]{"1"},
		Enabled:  true,
	}

	// Create test request
	req := httptest.NewRequest("POST", "/test-code/mainnet", strings.NewReader(`{"test":"headers"}`))
	req.Header.Set("Content-Type", "application/json")

	// Add request context
	rCtx := &requestContext{
		start:        time.Now(),
		method:       "POST",
		originUrl:    req.URL.String(),
		requestBody:  &bytes.Buffer{},
		responseBody: &bytes.Buffer{},
	}
	ctx := context.WithValue(req.Context(), "rCtx", rCtx)
	req = req.WithContext(ctx)

	// Get proxy and setup context
	network, rpcEndpoint, proxy, err := service.GetRPCProxy(node, "mainnet")
	require.NoError(t, err)

	rCtx.node = node
	rCtx.endpoint = rpcEndpoint
	rCtx.network = network

	// Execute proxy request
	recorder := httptest.NewRecorder()
	proxy.ServeHTTP(recorder, req)

	// Verify response
	assert.Equal(t, 200, recorder.Code)
	assert.Equal(t, `{"result":"header_test"}`, recorder.Body.String())

	// Verify response headers are forwarded
	assert.Equal(t, "application/json", recorder.Header().Get("Content-Type"))
	assert.Equal(t, "custom-value", recorder.Header().Get("X-Custom-Response"))
	assert.Equal(t, "no-cache", recorder.Header().Get("Cache-Control"))
	assert.Equal(t, "100", recorder.Header().Get("X-Rate-Limit-Remaining"))
}

func TestService_ProxyIntegration_ConcurrentRequests(t *testing.T) {
	// Setup mock upstream server
	mockServer := NewMockUpstreamServer(`{"result":"concurrent_test"}`, 200)
	defer mockServer.Close()

	// Setup service
	settings := model.RPCSettings{
		DefaultUpstream: mockServer.URL,
	}
	service := NewTestService(settings)

	// Setup chaininfo
	chaininfo.SlugToInfo = map[string]*chaininfo.EthChainInfo{
		"mainnet": {
			ChainID: "1",
			Name:    "Ethereum",
			Slug:    "mainnet",
		},
	}

	node := &model.RPCNode{
		ID:       "test-node",
		Networks: datatypes.JSONSlice[string]{"1"},
		Enabled:  true,
	}

	// Get proxy once (should be reused for all requests)
	network, rpcEndpoint, proxy, err := service.GetRPCProxy(node, "mainnet")
	require.NoError(t, err)

	// Execute multiple concurrent requests
	const numRequests = 10
	results := make(chan int, numRequests)

	for i := 0; i < numRequests; i++ {
		go func(requestID int) {
			// Create unique request
			requestBody := fmt.Sprintf(`{"id":%d,"method":"eth_blockNumber"}`, requestID)
			req := httptest.NewRequest("POST", "/test-code/mainnet", strings.NewReader(requestBody))
			req.Header.Set("Content-Type", "application/json")

			// Add request context
			rCtx := &requestContext{
				start:        time.Now(),
				method:       "POST",
				originUrl:    req.URL.String(),
				requestBody:  &bytes.Buffer{},
				responseBody: &bytes.Buffer{},
				node:         node,
				endpoint:     rpcEndpoint,
				network:      network,
			}
			ctx := context.WithValue(req.Context(), "rCtx", rCtx)
			req = req.WithContext(ctx)

			// Execute proxy request
			recorder := httptest.NewRecorder()
			proxy.ServeHTTP(recorder, req)

			results <- recorder.Code
		}(i)
	}

	// Wait for all requests to complete
	for i := 0; i < numRequests; i++ {
		statusCode := <-results
		assert.Equal(t, 200, statusCode)
	}

	// Verify all requests were received by upstream
	assert.Equal(t, numRequests, len(mockServer.ReceivedRequests))

	// Verify each request was unique
	requestIDs := make(map[string]bool)
	for _, req := range mockServer.ReceivedRequests {
		requestIDs[req.Body] = true
	}
	assert.Equal(t, numRequests, len(requestIDs), "All requests should be unique")
}

func TestService_ProxyIntegration_DefaultUpstreamFallback(t *testing.T) {
	// Setup mock default upstream server
	defaultServer := NewMockUpstreamServer(`{"result":"default_upstream"}`, 200)
	defer defaultServer.Close()

	// Setup service with default upstream but no specific network config
	settings := model.RPCSettings{
		DefaultUpstream: defaultServer.URL,
		Networks:        []model.RPCNetwork{}, // No specific network configs
	}
	service := NewTestService(settings)

	// Setup chaininfo for a network not in settings
	chaininfo.SlugToInfo = map[string]*chaininfo.EthChainInfo{
		"arbitrum": {
			ChainID: "42161",
			Name:    "Arbitrum One",
			Slug:    "arbitrum",
		},
	}

	node := &model.RPCNode{
		ID:       "test-node",
		Networks: datatypes.JSONSlice[string]{"*"}, // Allow all networks
		Enabled:  true,
	}

	// Create test request for network not in settings
	req := httptest.NewRequest("POST", "/test-code/arbitrum", strings.NewReader(`{"method":"eth_chainId"}`))
	req.Header.Set("Content-Type", "application/json")

	// Add request context
	rCtx := &requestContext{
		start:        time.Now(),
		method:       "POST",
		originUrl:    req.URL.String(),
		requestBody:  &bytes.Buffer{},
		responseBody: &bytes.Buffer{},
	}
	ctx := context.WithValue(req.Context(), "rCtx", rCtx)
	req = req.WithContext(ctx)

	// Get proxy (should fallback to default upstream)
	network, rpcEndpoint, proxy, err := service.GetRPCProxy(node, "arbitrum")
	require.NoError(t, err)
	require.NotNil(t, proxy)

	// Verify the network was created from chaininfo
	assert.Equal(t, "42161", network.ID)
	assert.Equal(t, "Arbitrum One", network.Name)
	assert.Equal(t, "/arbitrum", rpcEndpoint.Path)
	assert.Contains(t, rpcEndpoint.Upstream, defaultServer.URL)

	rCtx.node = node
	rCtx.endpoint = rpcEndpoint
	rCtx.network = network

	// Execute proxy request
	recorder := httptest.NewRecorder()
	proxy.ServeHTTP(recorder, req)

	// Verify response
	assert.Equal(t, 200, recorder.Code)
	assert.Equal(t, `{"result":"default_upstream"}`, recorder.Body.String())

	// Verify default upstream received the request
	require.Len(t, defaultServer.ReceivedRequests, 1)
	upstreamReq := defaultServer.GetLastRequest()
	assert.Equal(t, "POST", upstreamReq.Method)
	assert.Equal(t, `{"method":"eth_chainId"}`, upstreamReq.Body)
	assert.Contains(t, upstreamReq.URL, "/arbitrum")
}

// TestInternalRPCNode tests that internal nodes accept all networks and use default upstream
func (suite *RPCNodeIntegrationTestSuite) TestInternalRPCNode() {

	// Create internal RPC node
	internalNode := &model.RPCNode{
		ID:       "internal-node-123",
		Type:     "internal",
		Networks: datatypes.JSONSlice[string]{"*"}, // Internal nodes accept all networks
		Enabled:  true,
	}

	// Test multiple network slugs to verify internal node accepts all
	testCases := []struct {
		slug            string
		expectedChainID string
		requestBody     string
	}{
		{
			slug:            "mainnet",
			expectedChainID: "1",
			requestBody:     `{"method":"eth_blockNumber","params":[],"id":1}`,
		},
		{
			slug:            "polygon",
			expectedChainID: "137",
			requestBody:     `{"method":"eth_chainId","params":[],"id":2}`,
		},
		{
			slug:            "arbitrum",
			expectedChainID: "42161",
			requestBody:     `{"method":"eth_getBalance","params":["0x123","latest"],"id":3}`,
		},
	}

	for _, tc := range testCases {
		suite.Run(fmt.Sprintf("internal_node_accepts_%s", tc.slug), func() {
			// Reset mock server for each test
			suite.defaultServer.Reset()

			// Create test request
			req := httptest.NewRequest("POST", fmt.Sprintf("/internal-code/%s", tc.slug), strings.NewReader(tc.requestBody))
			req.Header.Set("Content-Type", "application/json")
			req.Header.Set("X-Internal-Request", "true")
			req = suite.createRequestContext(req)

			// Get proxy for the internal node
			network, rpcEndpoint, proxy, err := suite.service.GetRPCProxy(internalNode, tc.slug)
			suite.Require().NoError(err)
			suite.Require().NotNil(proxy)

			// Verify network configuration
			suite.Equal(tc.expectedChainID, network.ID)
			suite.Equal(fmt.Sprintf("/%s", tc.slug), rpcEndpoint.Path)

			// Internal nodes may use configured upstream or default upstream depending on implementation
			// The key is that they accept all networks
			suite.True(internalNode.IsNetworkAllowed(tc.expectedChainID), "Internal node should allow all networks")

			// Set context values
			rCtx := req.Context().Value("rCtx").(*requestContext)
			rCtx.node = internalNode
			rCtx.endpoint = rpcEndpoint
			rCtx.network = network

			// Execute proxy request
			recorder := httptest.NewRecorder()
			proxy.ServeHTTP(recorder, req)

			// Verify response
			suite.Equal(200, recorder.Code)
			suite.Contains(recorder.Body.String(), "result")

			// Verify that some upstream received the request (could be default or configured)
			totalRequests := len(suite.defaultServer.ReceivedRequests) + len(suite.mockServer.ReceivedRequests)
			suite.GreaterOrEqual(totalRequests, 1, "Some upstream should receive the request")

			// Find which server received the request
			var upstreamReq *MockRequest
			if len(suite.defaultServer.ReceivedRequests) > 0 {
				upstreamReq = suite.defaultServer.GetLastRequest()
			} else if len(suite.mockServer.ReceivedRequests) > 0 {
				upstreamReq = suite.mockServer.GetLastRequest()
			}

			suite.NotNil(upstreamReq, "Should have received a request")
			suite.Equal("POST", upstreamReq.Method)
			suite.Equal(tc.requestBody, upstreamReq.Body)
			suite.Equal("application/json", upstreamReq.Headers.Get("Content-Type"))
		})
	}
}

// TestInternalRPCNodeAllowsAllNetworks tests that internal nodes allow all networks
func (suite *RPCNodeIntegrationTestSuite) TestInternalRPCNodeAllowsAllNetworks() {
	// Create internal RPC node
	internalNode := &model.RPCNode{
		ID:       "internal-node-all-networks",
		Type:     "internal",
		Networks: datatypes.JSONSlice[string]{"*"},
		Enabled:  true,
	}

	// Test that internal node allows various network IDs
	testNetworkIDs := []string{"1", "137", "42161", "10", "999", "unknown"}

	for _, networkID := range testNetworkIDs {
		suite.Run(fmt.Sprintf("allows_network_%s", networkID), func() {
			allowed := internalNode.IsNetworkAllowed(networkID)
			suite.True(allowed, "Internal node should allow network %s", networkID)
		})
	}

	// Test that internal node returns correct effective networks
	effectiveNetworks := internalNode.GetEffectiveNetworks()
	suite.Equal([]string{"*"}, effectiveNetworks, "Internal node should return '*' as effective networks")
}

// TestRPCNodeIntegrationSuite runs the integration test suite
func TestRPCNodeIntegrationSuite(t *testing.T) {
	suite.Run(t, new(RPCNodeIntegrationTestSuite))
}
