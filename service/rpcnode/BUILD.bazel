load("@rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "rpcnode",
    srcs = [
        "copy_reader.go",
        "request_context.go",
        "rpcnode_proxy.go",
        "rpcnode_service.go",
    ],
    importpath = "sentioxyz/sentio/service/rpcnode",
    visibility = ["//visibility:public"],
    deps = [
        "//chain/evm/chaininfo",
        "//common/gonanoid",
        "//common/log",
        "//common/utils",
        "//service/common/models",
        "//service/common/preloader",
        "//service/common/protos",
        "//service/common/redis",
        "//service/rpcnode/model",
        "//service/rpcnode/protos",
        "//service/rpcnode/repository",
        "//service/usage/protos",
        "//service/usage/utils",
        "@com_github_clickhouse_clickhouse_go_v2//:clickhouse-go",
        "@com_github_go_redis_redis_rate_v10//:redis_rate",
        "@com_github_grpc_ecosystem_grpc_gateway_v2//runtime",
        "@com_github_redis_go_redis_v9//:go-redis",
        "@io_gorm_datatypes//:datatypes",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//types/known/emptypb",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "rpcnode_test",
    srcs = [
        "rpcnode_integration_test.go",
        "rpcnode_proxy_test.go",
    ],
    data = [
        "//service/rpcnode/server:default.yaml",
    ],
    embed = [":rpcnode"],
    deps = [
        "//chain/evm/chaininfo",
        "//service/common/models",
        "//service/processor/models",
        "//service/rpcnode/model",
        "//service/usage/utils",
        "@com_github_go_redis_redis_rate_v10//:redis_rate",
        "@com_github_redis_go_redis_v9//:go-redis",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@com_github_stretchr_testify//require",
        "@com_github_stretchr_testify//suite",
        "@io_gorm_datatypes//:datatypes",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
    ],
)
