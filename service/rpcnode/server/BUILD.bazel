load("@rules_go//go:def.bzl", "go_binary", "go_library")
load("//bazel:images.bzl", "create_go_image")

exports_files(["default.yaml"])

go_library(
    name = "server_lib",
    srcs = ["main.go"],
    importpath = "sentioxyz/sentio/service/rpcnode/server",
    visibility = ["//visibility:private"],
    deps = [
        "//common/clickhouse",
        "//common/flags",
        "//common/log",
        "//common/monitoring",
        "//service/common/auth",
        "//service/common/preloader",
        "//service/common/rpc",
        "//service/endpoint",
        "//service/rpcnode",
        "//service/rpcnode/model",
        "//service/rpcnode/preloaders",
        "//service/rpcnode/protos",
        "//service/rpcnode/repository",
        "//service/usage/utils",
        "@com_github_grpc_ecosystem_grpc_gateway_v2//runtime",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_x_net//context",
    ],
)

go_binary(
    name = "server",
    embed = [":server_lib"],
    visibility = ["//visibility:public"],
)

create_go_image(
    binary = ":server",
)
